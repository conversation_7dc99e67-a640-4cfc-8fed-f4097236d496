<?php
/**
 * PFG Core Diagnostic Script
 * Run this script to verify PFG Core module installation and configuration
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

echo "=== PFG Core Module Diagnostic ===\n\n";

// Check if module is declared
$moduleFile = 'app/etc/modules/PFG_Core.xml';
echo "1. Module Declaration: ";
if (file_exists($moduleFile)) {
    echo "✓ Found\n";
    $xml = simplexml_load_file($moduleFile);
    $active = (string)$xml->modules->PFG_Core->active;
    echo "   Status: " . ($active === 'true' ? '✓ Active' : '✗ Inactive') . "\n";
} else {
    echo "✗ Missing\n";
}

// Check if module is loaded by Magento
echo "\n2. Module Loading: ";
$config = Mage::getConfig();
$moduleConfig = $config->getModuleConfig('PFG_Core');
if ($moduleConfig && $moduleConfig->is('active')) {
    echo "✓ Loaded and Active\n";
    echo "   Version: " . (string)$moduleConfig->version . "\n";
} else {
    echo "✗ Not Loaded\n";
}

// Check module files
echo "\n3. Module Files:\n";
$files = array(
    'app/code/local/PFG/Core/etc/config.xml' => 'Main Configuration',
    'app/code/local/PFG/Core/etc/system.xml' => 'System Configuration',
    'app/code/local/PFG/Core/etc/adminhtml.xml' => 'Admin Configuration',
    'app/code/local/PFG/Core/Helper/Data.php' => 'Main Helper',
    'app/code/local/PFG/Core/Model/Installation.php' => 'Installation Model',
    'app/code/local/PFG/Core/controllers/Adminhtml/Pfg/CoreController.php' => 'Admin Controller'
);

foreach ($files as $file => $description) {
    echo "   " . $description . ": ";
    echo file_exists($file) ? "✓ Found\n" : "✗ Missing\n";
}

// Check frontend models
echo "\n4. Frontend Model Blocks:\n";
$blocks = array(
    'app/code/local/PFG/Core/Block/Adminhtml/System/Config/Button/Test.php' => 'Test Button Block',
    'app/code/local/PFG/Core/Block/Adminhtml/System/Config/Status.php' => 'Status Block',
    'app/code/local/PFG/Core/Block/Adminhtml/System/Config/Repository/Management.php' => 'Repository Management Block',
    'app/code/local/PFG/Core/Block/Adminhtml/System/Config/Installation/History.php' => 'Installation History Block',
    'app/code/local/PFG/Core/Block/Adminhtml/System/Config/Backup/Management.php' => 'Backup Management Block',
    'app/code/local/PFG/Core/Block/Adminhtml/System/Config/System/Logs.php' => 'System Logs Block'
);

foreach ($blocks as $file => $description) {
    echo "   " . $description . ": ";
    echo file_exists($file) ? "✓ Found\n" : "✗ Missing\n";
}

// Check templates
echo "\n5. Template Files:\n";
$templates = array(
    'app/design/adminhtml/default/default/template/pfg_core/system/config/button/test.phtml' => 'Test Button Template',
    'app/design/adminhtml/default/default/template/pfg_core/system/config/status.phtml' => 'Status Template',
    'app/design/adminhtml/default/default/template/pfg_core/system/config/repository/management.phtml' => 'Repository Management Template',
    'app/design/adminhtml/default/default/template/pfg_core/system/config/installation/history.phtml' => 'Installation History Template',
    'app/design/adminhtml/default/default/template/pfg_core/system/config/backup/management.phtml' => 'Backup Management Template',
    'app/design/adminhtml/default/default/template/pfg_core/system/config/system/logs.phtml' => 'System Logs Template'
);

foreach ($templates as $file => $description) {
    echo "   " . $description . ": ";
    echo file_exists($file) ? "✓ Found\n" : "✗ Missing\n";
}

// Check database tables
echo "\n6. Database Tables:\n";
$tables = array(
    'pfg_core_installation' => 'Installation History',
    'pfg_core_backup' => 'Backup Records',
    'pfg_core_audit_log' => 'Audit Log'
);

$resource = Mage::getSingleton('core/resource');
$connection = $resource->getConnection('core_read');

foreach ($tables as $table => $description) {
    $tableName = $resource->getTableName($table);
    echo "   " . $description . ": ";
    echo $connection->isTableExists($tableName) ? "✓ Exists\n" : "✗ Missing\n";
}

// Check system configuration
echo "\n7. System Configuration:\n";
try {
    $helper = Mage::helper('pfg_core');
    echo "   Helper Class: ✓ Loaded\n";
    
    $enabled = Mage::getStoreConfig('pfg/core/enabled');
    echo "   Module Enabled: " . ($enabled ? '✓ Yes' : '✗ No') . "\n";
    
    $workspace = Mage::getStoreConfig('pfg/core/bitbucket_workspace');
    echo "   Bitbucket Workspace: " . ($workspace ? $workspace : 'Not Set') . "\n";
    
    $project = Mage::getStoreConfig('pfg/core/bitbucket_project');
    echo "   Bitbucket Project: " . ($project ? $project : 'Not Set') . "\n";
    
} catch (Exception $e) {
    echo "   ✗ Error: " . $e->getMessage() . "\n";
}

// Check cache status
echo "\n8. Cache Status:\n";
$cacheTypes = Mage::app()->getCacheInstance()->getTypes();
foreach ($cacheTypes as $type => $info) {
    if (in_array($type, array('config', 'layout', 'block_html'))) {
        $status = Mage::app()->getCacheInstance()->canUse($type);
        echo "   " . ucfirst($type) . " Cache: " . ($status ? '✓ Enabled' : '✗ Disabled') . "\n";
    }
}

echo "\n=== Diagnostic Complete ===\n";
echo "\nIf all items show ✓, the module should be visible in System > Configuration > PFG\n";
echo "If any items show ✗, please check the installation and clear cache.\n\n";
echo "To clear cache: docker exec vip_watches_cache redis-cli FLUSHALL\n";
echo "Then refresh System > Configuration page.\n";
?>
