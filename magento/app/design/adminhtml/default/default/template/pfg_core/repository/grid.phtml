<?php
/**
 * PFG Core Repository Grid Template
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$ajaxUrls = $this->getAjaxUrls();
$formKey = $this->getFormKey();
?>

<div id="pfg-core-repository-container">
    <?php if (!$this->isConfigured()): ?>
        <div class="error-msg">
            <ul>
                <li>
                    <span>
                        <?php echo $this->__('PFG Core is not properly configured. Please configure Bitbucket credentials in ') ?>
                        <a href="<?php echo $this->getUrl('adminhtml/system_config/edit/section/pfg') ?>">
                            <?php echo $this->__('System > Configuration > PFG > Core Module Management') ?>
                        </a>
                    </span>
                </li>
            </ul>
        </div>
    <?php else: ?>
        <div id="pfg-core-loading" style="text-align: center; padding: 20px; display: none;">
            <img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo $this->__('Loading...') ?>" />
            <br />
            <?php echo $this->__('Loading repositories...') ?>
        </div>
        
        <div id="pfg-core-error" style="display: none;" class="error-msg">
            <ul><li><span id="pfg-core-error-message"></span></li></ul>
        </div>
        
        <div id="pfg-core-repositories" style="display: none;">
            <div class="grid">
                <table cellspacing="0" class="data">
                    <thead>
                        <tr class="headings">
                            <th><?php echo $this->__('Repository') ?></th>
                            <th><?php echo $this->__('Description') ?></th>
                            <th><?php echo $this->__('Module Name') ?></th>
                            <th><?php echo $this->__('Status') ?></th>
                            <th><?php echo $this->__('Installed Version') ?></th>
                            <th><?php echo $this->__('Latest Version') ?></th>
                            <th><?php echo $this->__('Actions') ?></th>
                        </tr>
                    </thead>
                    <tbody id="pfg-core-repository-tbody">
                        <!-- Repository rows will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Installation Progress Modal -->
<div id="pfg-core-install-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 5px; min-width: 400px;">
        <h3 id="pfg-core-install-title"><?php echo $this->__('Installing Module...') ?></h3>
        <div id="pfg-core-install-progress">
            <div style="text-align: center; padding: 20px;">
                <img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo $this->__('Loading...') ?>" />
                <br />
                <span id="pfg-core-install-status"><?php echo $this->__('Preparing installation...') ?></span>
            </div>
        </div>
        <div id="pfg-core-install-result" style="display: none;">
            <div id="pfg-core-install-message"></div>
            <div style="text-align: right; margin-top: 15px;">
                <button type="button" onclick="pfgCoreCloseInstallModal()" class="scalable">
                    <span><span><span><?php echo $this->__('Close') ?></span></span></span>
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
//<![CDATA[
var pfgCoreAjaxUrls = <?php echo Mage::helper('core')->jsonEncode($ajaxUrls) ?>;
var pfgCoreFormKey = '<?php echo $formKey ?>';

// Load repositories on page load
document.observe('dom:loaded', function() {
    <?php if ($this->isConfigured()): ?>
        pfgCoreLoadRepositories();
    <?php endif; ?>
});

function pfgCoreRefreshRepositories() {
    pfgCoreLoadRepositories();
}

function pfgCoreLoadRepositories() {
    $('pfg-core-loading').show();
    $('pfg-core-error').hide();
    $('pfg-core-repositories').hide();
    
    new Ajax.Request(pfgCoreAjaxUrls.get_repositories, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                if (result.success) {
                    pfgCoreDisplayRepositories(result.data);
                } else {
                    pfgCoreShowError(result.error || 'Unknown error occurred');
                }
            } catch (e) {
                pfgCoreShowError('Invalid response from server');
            }
        },
        onFailure: function() {
            pfgCoreShowError('Failed to load repositories');
        },
        onComplete: function() {
            $('pfg-core-loading').hide();
        }
    });
}

function pfgCoreDisplayRepositories(repositories) {
    var tbody = $('pfg-core-repository-tbody');
    tbody.innerHTML = '';
    
    if (repositories.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;"><?php echo $this->__('No repositories found') ?></td></tr>';
    } else {
        repositories.each(function(repo) {
            var row = pfgCoreCreateRepositoryRow(repo);
            tbody.appendChild(row);
        });
    }
    
    $('pfg-core-repositories').show();
}

function pfgCoreCreateRepositoryRow(repo) {
    var row = document.createElement('tr');
    
    // Repository name
    var nameCell = document.createElement('td');
    nameCell.innerHTML = '<strong>' + repo.name.escapeHTML() + '</strong>';
    row.appendChild(nameCell);
    
    // Description
    var descCell = document.createElement('td');
    descCell.innerHTML = repo.description ? repo.description.escapeHTML() : '-';
    row.appendChild(descCell);
    
    // Module name
    var moduleCell = document.createElement('td');
    moduleCell.innerHTML = repo.module_name.escapeHTML();
    row.appendChild(moduleCell);
    
    // Status
    var statusCell = document.createElement('td');
    var statusHtml = '';
    switch (repo.installation_status) {
        case 'installed':
            statusHtml = '<span style="color: #3d6611; font-weight: bold;">✓ <?php echo $this->__('Installed') ?></span>';
            break;
        case 'update_available':
            statusHtml = '<span style="color: #f18500; font-weight: bold;">⚠ <?php echo $this->__('Update Available') ?></span>';
            break;
        case 'not_installed':
            statusHtml = '<span style="color: #666;">- <?php echo $this->__('Not Installed') ?></span>';
            break;
    }
    statusCell.innerHTML = statusHtml;
    row.appendChild(statusCell);
    
    // Installed version
    var installedCell = document.createElement('td');
    installedCell.innerHTML = repo.installed_version ? repo.installed_version.escapeHTML() : '-';
    row.appendChild(installedCell);
    
    // Latest version
    var latestCell = document.createElement('td');
    latestCell.innerHTML = repo.latest_version ? repo.latest_version.escapeHTML() : '-';
    row.appendChild(latestCell);
    
    // Actions
    var actionsCell = document.createElement('td');
    var actionsHtml = '';
    
    if (!repo.is_installed && repo.latest_version) {
        actionsHtml += '<button type="button" onclick="pfgCoreInstallModule(\'' + repo.name + '\', \'' + repo.latest_version + '\')" class="scalable" style="margin-right: 5px;">';
        actionsHtml += '<span><span><span><?php echo $this->__('Install') ?></span></span></span>';
        actionsHtml += '</button>';
    }
    
    if (repo.has_update && repo.latest_version) {
        actionsHtml += '<button type="button" onclick="pfgCoreUpdateModule(\'' + repo.name + '\', \'' + repo.latest_version + '\')" class="scalable" style="margin-right: 5px;">';
        actionsHtml += '<span><span><span><?php echo $this->__('Update') ?></span></span></span>';
        actionsHtml += '</button>';
    }
    
    if (repo.html_url) {
        actionsHtml += '<a href="' + repo.html_url + '" target="_blank" style="text-decoration: none;">';
        actionsHtml += '<button type="button" class="scalable">';
        actionsHtml += '<span><span><span><?php echo $this->__('View') ?></span></span></span>';
        actionsHtml += '</button>';
        actionsHtml += '</a>';
    }
    
    actionsCell.innerHTML = actionsHtml;
    row.appendChild(actionsCell);
    
    return row;
}

function pfgCoreInstallModule(repositoryName, version) {
    if (!confirm('<?php echo $this->__('Are you sure you want to install this module? This action will create a backup and install the module files.') ?>')) {
        return;
    }
    
    pfgCoreShowInstallModal('<?php echo $this->__('Installing Module') ?>', '<?php echo $this->__('Preparing installation...') ?>');
    
    new Ajax.Request(pfgCoreAjaxUrls.install_module, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey,
            'repository': repositoryName,
            'version': version
        },
        onSuccess: function(response) {
            pfgCoreHandleInstallResponse(response);
        },
        onFailure: function() {
            pfgCoreShowInstallResult(false, '<?php echo $this->__('Installation failed') ?>');
        }
    });
}

function pfgCoreUpdateModule(repositoryName, version) {
    if (!confirm('<?php echo $this->__('Are you sure you want to update this module? This action will create a backup and update the module files.') ?>')) {
        return;
    }
    
    pfgCoreShowInstallModal('<?php echo $this->__('Updating Module') ?>', '<?php echo $this->__('Preparing update...') ?>');
    
    new Ajax.Request(pfgCoreAjaxUrls.update_module, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey,
            'repository': repositoryName,
            'version': version
        },
        onSuccess: function(response) {
            pfgCoreHandleInstallResponse(response);
        },
        onFailure: function() {
            pfgCoreShowInstallResult(false, '<?php echo $this->__('Update failed') ?>');
        }
    });
}

function pfgCoreHandleInstallResponse(response) {
    try {
        var result = response.responseText.evalJSON();
        
        if (result.success) {
            pfgCoreShowInstallResult(true, result.message);
            // Trigger post-installation actions
            pfgCorePostInstallActions();
        } else {
            pfgCoreShowInstallResult(false, result.error || '<?php echo $this->__('Installation failed') ?>');
        }
    } catch (e) {
        pfgCoreShowInstallResult(false, '<?php echo $this->__('Invalid response from server') ?>');
    }
}

function pfgCorePostInstallActions() {
    $('pfg-core-install-status').innerHTML = '<?php echo $this->__('Clearing caches and performing cleanup...') ?>';
    
    new Ajax.Request(pfgCoreAjaxUrls.post_install_actions, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey
        },
        onComplete: function() {
            // Refresh repositories list
            setTimeout(function() {
                pfgCoreLoadRepositories();
            }, 1000);
        }
    });
}

function pfgCoreShowInstallModal(title, status) {
    $('pfg-core-install-title').innerHTML = title;
    $('pfg-core-install-status').innerHTML = status;
    $('pfg-core-install-progress').show();
    $('pfg-core-install-result').hide();
    $('pfg-core-install-modal').show();
}

function pfgCoreShowInstallResult(success, message) {
    $('pfg-core-install-progress').hide();
    
    var messageDiv = $('pfg-core-install-message');
    if (success) {
        messageDiv.className = 'success-msg';
        messageDiv.innerHTML = '<ul><li><span>' + message.escapeHTML() + '</span></li></ul>';
    } else {
        messageDiv.className = 'error-msg';
        messageDiv.innerHTML = '<ul><li><span>' + message.escapeHTML() + '</span></li></ul>';
    }
    
    $('pfg-core-install-result').show();
}

function pfgCoreCloseInstallModal() {
    $('pfg-core-install-modal').hide();
}

function pfgCoreShowError(message) {
    $('pfg-core-error-message').innerHTML = message.escapeHTML();
    $('pfg-core-error').show();
}
//]]>
</script>

<style type="text/css">
#pfg-core-repository-container .data th,
#pfg-core-repository-container .data td {
    padding: 8px 10px;
    border-bottom: 1px solid #ddd;
    vertical-align: top;
}

#pfg-core-repository-container .data th {
    background-color: #f8f8f8;
    font-weight: bold;
    text-align: left;
}

#pfg-core-repository-container .data tr:hover {
    background-color: #f5f5f5;
}

#pfg-core-install-modal button {
    margin-left: 10px;
}
</style>
