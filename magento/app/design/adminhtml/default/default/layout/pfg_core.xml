<?xml version="1.0"?>
<!--
/**
 * PFG Core Admin Layout
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<layout version="0.1.0">
    <!-- Module Manager Page -->
    <adminhtml_pfg_core_index>
        <reference name="content">
            <block type="pfg_core/adminhtml_repository" name="pfg.core.repository" />
        </reference>
    </adminhtml_pfg_core_index>
    
    <!-- Repository Browser Page -->
    <adminhtml_pfg_core_repositories>
        <reference name="content">
            <block type="pfg_core/adminhtml_repository" name="pfg.core.repository" />
        </reference>
    </adminhtml_pfg_core_repositories>
    
    <!-- Installation History Page -->
    <adminhtml_pfg_core_installations>
        <reference name="content">
            <block type="pfg_core/adminhtml_installation" name="pfg.core.installation" />
        </reference>
    </adminhtml_pfg_core_installations>
    
    <!-- Backup Management Page -->
    <adminhtml_pfg_core_backups>
        <reference name="content">
            <block type="pfg_core/adminhtml_backup" name="pfg.core.backup" />
        </reference>
    </adminhtml_pfg_core_backups>

    <!-- System Logs Page -->
    <adminhtml_pfg_core_logs>
        <reference name="content">
            <block type="pfg_core/adminhtml_log" name="pfg.core.log" />
        </reference>
    </adminhtml_pfg_core_logs>

    <!-- Log Grid AJAX -->
    <adminhtml_pfg_core_loggrid>
        <block type="pfg_core/adminhtml_log_grid" name="pfg.core.log.grid" />
    </adminhtml_pfg_core_loggrid>
</layout>
