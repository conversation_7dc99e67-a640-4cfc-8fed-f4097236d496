"PFG","PFG"
"PFG Configuration","PFG Configuration"
"Core Module Management","Core Module Management"
"Enable PFG Core","Enable PFG Core"
"Enable automated module management through Bitbucket integration","Enable automated module management through Bitbucket integration"
"Bitbucket Username","Bitbucket Username"
"Your Bitbucket username for API authentication","Your Bitbucket username for API authentication"
"Bitbucket App Password","Bitbucket App Password"
"Bitbucket App Password with repository read permissions","Bitbucket App Password with repository read permissions"
"Bitbucket Workspace","Bitbucket Workspace"
"Bitbucket workspace name (default: pfg)","Bitbucket workspace name (default: pfg)"
"Bitbucket Project Key","Bitbucket Project Key"
"Bitbucket project key (default: LABS)","Bitbucket project key (default: LABS)"
"Test Connection","Test Connection"
"Test Bitbucket API connection and display status","Test Bitbucket API connection and display status"
"Connection Status","Connection Status"
"Advanced Settings","Advanced Settings"
"API Timeout (seconds)","API Timeout (seconds)"
"Timeout for Bitbucket API requests","Timeout for Bitbucket API requests"
"Backup Retention (days)","Backup Retention (days)"
"Number of days to keep backup files","Number of days to keep backup files"
"Log Level","Log Level"
"Logging verbosity level","Logging verbosity level"
"Module Manager","Module Manager"
"Repository Browser","Repository Browser"
"Installation History","Installation History"
"Backup Management","Backup Management"
"Emergency","Emergency"
"Alert","Alert"
"Critical","Critical"
"Error","Error"
"Warning","Warning"
"Notice","Notice"
"Info","Info"
"Debug","Debug"
