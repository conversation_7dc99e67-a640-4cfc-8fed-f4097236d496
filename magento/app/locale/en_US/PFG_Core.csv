"PFG","PFG"
"PFG Configuration","PFG Configuration"
"Core Module Management","Core Module Management"
"Enable PFG Core","Enable PFG Core"
"Enable automated module management through Bitbucket integration","Enable automated module management through Bitbucket integration"
"Bitbucket Username","Bitbucket Username"
"Your Bitbucket username for API authentication","Your Bitbucket username for API authentication"
"Bitbucket App Password","Bitbucket App Password"
"Bitbucket App Password with repository read permissions","Bitbucket App Password with repository read permissions"
"Bitbucket Workspace","Bitbucket Workspace"
"Bitbucket workspace name (default: pfg)","Bitbucket workspace name (default: pfg)"
"Bitbucket Project Key","Bitbucket Project Key"
"Bitbucket project key (default: LABS)","Bitbucket project key (default: LABS)"
"Test Connection","Test Connection"
"Test Bitbucket API connection and display status","Test Bitbucket API connection and display status"
"Connection Status","Connection Status"
"Advanced Settings","Advanced Settings"
"API Timeout (seconds)","API Timeout (seconds)"
"Timeout for Bitbucket API requests","Timeout for Bitbucket API requests"
"Backup Retention (days)","Backup Retention (days)"
"Number of days to keep backup files","Number of days to keep backup files"
"Log Level","Log Level"
"Logging verbosity level","Logging verbosity level"
"Repository Management","Repository Management"
"Installation & Backup Management","Installation & Backup Management"
"Emergency","Emergency"
"Alert","Alert"
"Critical","Critical"
"Error","Error"
"Warning","Warning"
"Notice","Notice"
"Info","Info"
"Debug","Debug"
"Browse and manage PFG modules from Bitbucket repositories","Browse and manage PFG modules from Bitbucket repositories"
"View and manage module installation history","View and manage module installation history"
"Manage module installation backups","Manage module installation backups"
"View PFG Core system logs and audit trail","View PFG Core system logs and audit trail"
"Please configure Bitbucket credentials above to enable repository management.","Please configure Bitbucket credentials above to enable repository management."
"Refresh Repositories","Refresh Repositories"
"No repositories found","No repositories found"
"No installation history found.","No installation history found."
"No backup files found.","No backup files found."
"No log entries found.","No log entries found."
"Showing last %d installations","Showing last %d installations"
"Showing last %d backups","Showing last %d backups"
"Showing last %d log entries","Showing last %d log entries"
"Are you sure you want to rollback this installation? This will restore files from the backup.","Are you sure you want to rollback this installation? This will restore files from the backup."
"Are you sure you want to restore from this backup? This will overwrite current files and cannot be undone.","Are you sure you want to restore from this backup? This will overwrite current files and cannot be undone."
"Are you sure you want to cleanup old backup files? This will remove backups older than the retention period.","Are you sure you want to cleanup old backup files? This will remove backups older than the retention period."
"Are you sure you want to clear old log entries? This will remove logs older than 7 days.","Are you sure you want to clear old log entries? This will remove logs older than 7 days."
"Rolling back...","Rolling back..."
"Restoring...","Restoring..."
"Cleaning up...","Cleaning up..."
"Clearing...","Clearing..."
"Rollback completed successfully","Rollback completed successfully"
"Backup restored successfully","Backup restored successfully"
"Cleanup completed successfully","Cleanup completed successfully"
"Old log entries cleared successfully","Old log entries cleared successfully"
"Rollback failed: ","Rollback failed: "
"Restore failed: ","Restore failed: "
"Cleanup failed: ","Cleanup failed: "
"Rollback request failed","Rollback request failed"
"Restore request failed","Restore request failed"
"Cleanup request failed","Cleanup request failed"
"Clear logs request failed","Clear logs request failed"
"Download backup file","Download backup file"
"Restore from this backup","Restore from this backup"
"Rollback this installation","Rollback this installation"
