<?php
/**
 * PFG Core Repository Model
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_Repository extends Mage_Core_Model_Abstract
{
    /**
     * @var PFG_Core_Helper_Bitbucket
     */
    protected $_bitbucketHelper;
    
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_bitbucketHelper = Mage::helper('pfg_core/bitbucket');
        $this->_helper = Mage::helper('pfg_core');
    }
    
    /**
     * Get all repositories with installation status
     *
     * @return array
     */
    public function getRepositoriesWithStatus()
    {
        try {
            $response = $this->_bitbucketHelper->getRepositories();
            
            if (!$response['success']) {
                throw new Exception($response['error']);
            }
            
            $repositories = $response['data'];
            $installedModules = $this->_getInstalledModules();
            
            foreach ($repositories as &$repo) {
                $moduleName = $this->_getModuleNameFromRepository($repo['name']);
                $repo['module_name'] = $moduleName;
                $repo['is_installed'] = isset($installedModules[$moduleName]);
                $repo['installed_version'] = isset($installedModules[$moduleName]) ? $installedModules[$moduleName] : null;
                
                // Get latest version from tags
                $tagsResponse = $this->_bitbucketHelper->getRepositoryTags($repo['name']);
                if ($tagsResponse['success'] && !empty($tagsResponse['data'])) {
                    $repo['latest_version'] = $tagsResponse['data'][0]['name'];
                    $repo['has_update'] = $repo['is_installed'] && 
                        version_compare($repo['installed_version'], $repo['latest_version'], '<');
                } else {
                    $repo['latest_version'] = null;
                    $repo['has_update'] = false;
                }
                
                $repo['installation_status'] = $this->_getInstallationStatus($repo);
            }
            
            return array(
                'success' => true,
                'data' => $repositories
            );
            
        } catch (Exception $e) {
            $this->_helper->log('Failed to get repositories with status: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get repository details by name
     *
     * @param string $repositoryName
     * @return array
     */
    public function getRepositoryDetails($repositoryName)
    {
        try {
            $repositories = $this->getRepositoriesWithStatus();
            
            if (!$repositories['success']) {
                throw new Exception($repositories['error']);
            }
            
            foreach ($repositories['data'] as $repo) {
                if ($repo['name'] === $repositoryName) {
                    // Get additional details like tags
                    $tagsResponse = $this->_bitbucketHelper->getRepositoryTags($repositoryName);
                    if ($tagsResponse['success']) {
                        $repo['tags'] = $tagsResponse['data'];
                    }
                    
                    return array(
                        'success' => true,
                        'data' => $repo
                    );
                }
            }
            
            throw new Exception('Repository not found');
            
        } catch (Exception $e) {
            $this->_helper->log('Failed to get repository details: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Check if repository can be installed
     *
     * @param string $repositoryName
     * @return array
     */
    public function canInstallRepository($repositoryName)
    {
        try {
            $repoDetails = $this->getRepositoryDetails($repositoryName);

            if (!$repoDetails['success']) {
                throw new Exception($repoDetails['error']);
            }

            $repo = $repoDetails['data'];
            $checks = array();
            $canInstall = true;

            // System requirements validation
            $validator = Mage::helper('pfg_core/validator');
            $systemChecks = $validator->validateSystemRequirements();

            if (!$systemChecks['can_install']) {
                $canInstall = false;
            }

            $checks = array_merge($checks, $systemChecks['checks']);

            // Module compatibility validation
            $compatibilityChecks = $validator->validateModuleCompatibility($repo['module_name']);
            $checks = array_merge($checks, $compatibilityChecks['checks']);

            // Check if already installed
            if ($repo['is_installed']) {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->_helper->__('Module is already installed (version %s)', $repo['installed_version'])
                );
            }

            // Check module name format
            if (!$this->_helper->isValidModuleName($repo['module_name'])) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->_helper->__('Invalid module name format: %s', $repo['module_name'])
                );
                $canInstall = false;
            }

            // Security validation
            $securityChecks = $this->_validateModuleSecurity($repo);
            if (!$securityChecks['is_secure']) {
                $canInstall = false;
            }
            $checks = array_merge($checks, $securityChecks['checks']);

            // Admin user validation
            $adminChecks = $this->_validateAdminPermissions();
            if (!$adminChecks['has_permission']) {
                $canInstall = false;
            }
            $checks = array_merge($checks, $adminChecks['checks']);

            return array(
                'success' => true,
                'can_install' => $canInstall,
                'checks' => $checks
            );

        } catch (Exception $e) {
            $this->_helper->log('Failed to check repository installation: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Validate module security
     *
     * @param array $repo
     * @return array
     */
    protected function _validateModuleSecurity($repo)
    {
        $checks = array();
        $isSecure = true;

        // Check repository source
        if (!$repo['is_private']) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->_helper->__('Repository is public - ensure it contains trusted code')
            );
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->_helper->__('Repository is private ✓')
            );
        }

        // Check module namespace
        if (strpos($repo['module_name'], 'PFG_') !== 0) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->_helper->__('Module is not in PFG namespace - verify source')
            );
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->_helper->__('Module is in PFG namespace ✓')
            );
        }

        // Check for suspicious patterns in repository name
        $suspiciousPatterns = array('hack', 'exploit', 'backdoor', 'malware', 'virus');
        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($repo['name'], $pattern) !== false) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->_helper->__('Repository name contains suspicious pattern: %s', $pattern)
                );
                $isSecure = false;
            }
        }

        return array(
            'is_secure' => $isSecure,
            'checks' => $checks
        );
    }

    /**
     * Validate admin permissions
     *
     * @return array
     */
    protected function _validateAdminPermissions()
    {
        $checks = array();
        $hasPermission = true;

        $session = Mage::getSingleton('admin/session');

        // Check if admin is logged in
        if (!$session->isLoggedIn()) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->_helper->__('Admin session required')
            );
            $hasPermission = false;
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->_helper->__('Admin session active ✓')
            );

            // Check specific permissions
            if (!$session->isAllowed('pfg/core')) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->_helper->__('Insufficient permissions for PFG Core operations')
                );
                $hasPermission = false;
            } else {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->_helper->__('Admin has required permissions ✓')
                );
            }

            // Check if user is super admin
            $user = $session->getUser();
            if ($user && $user->getRole() && $user->getRole()->getRoleName() === 'Administrators') {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->_helper->__('User has administrator role ✓')
                );
            } else {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->_helper->__('User does not have administrator role - proceed with caution')
                );
            }
        }

        return array(
            'has_permission' => $hasPermission,
            'checks' => $checks
        );
    }
    
    /**
     * Get installed modules
     *
     * @return array
     */
    protected function _getInstalledModules()
    {
        $modules = array();
        $config = Mage::getConfig();
        
        foreach ($config->getNode('modules')->children() as $moduleName => $moduleConfig) {
            if ($moduleConfig->active == 'true') {
                $modules[$moduleName] = (string)$moduleConfig->version;
            }
        }
        
        return $modules;
    }
    
    /**
     * Get module name from repository name
     *
     * @param string $repositoryName
     * @return string
     */
    protected function _getModuleNameFromRepository($repositoryName)
    {
        // Convert repository name to module name format
        // e.g., "pfg-cloudflare-integration" -> "PFG_CloudflareIntegration"
        $parts = explode('-', $repositoryName);
        $moduleName = '';
        
        foreach ($parts as $part) {
            $moduleName .= ucfirst(strtolower($part));
        }
        
        // Ensure it starts with PFG_
        if (strpos($moduleName, 'Pfg') === 0) {
            $moduleName = 'PFG_' . substr($moduleName, 3);
        } elseif (strpos($moduleName, 'PFG') !== 0) {
            $moduleName = 'PFG_' . $moduleName;
        }
        
        return $moduleName;
    }
    
    /**
     * Get installation status
     *
     * @param array $repo
     * @return string
     */
    protected function _getInstallationStatus($repo)
    {
        if (!$repo['is_installed']) {
            return 'not_installed';
        }
        
        if ($repo['has_update']) {
            return 'update_available';
        }
        
        return 'installed';
    }
    
    /**
     * Check for module conflicts
     *
     * @param string $moduleName
     * @return array
     */
    protected function _checkModuleConflicts($moduleName)
    {
        $conflicts = array();
        
        // Check for existing module files
        $moduleDir = Mage::getBaseDir('code') . DS . 'local' . DS . str_replace('_', DS, $moduleName);
        if (is_dir($moduleDir)) {
            $conflicts[] = $this->_helper->__('Module directory already exists: %s', $moduleDir);
        }
        
        // Check for module declaration file
        $moduleFile = Mage::getBaseDir('app') . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
        if (file_exists($moduleFile)) {
            $conflicts[] = $this->_helper->__('Module declaration file already exists: %s', $moduleFile);
        }
        
        return $conflicts;
    }
}
