<?php
/**
 * PFG Core Admin Controller
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Adminhtml_Pfg_CoreController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Test Bitbucket connection
     */
    public function testConnectionAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $request = $this->getRequest();
            
            // Get credentials from request (for testing without saving)
            $username = $request->getParam('username');
            $appPassword = $request->getParam('app_password');
            $workspace = $request->getParam('workspace', 'pfg');
            $project = $request->getParam('project', 'LABS');
            
            if (empty($username) || empty($appPassword)) {
                throw new Exception($this->__('Username and App Password are required'));
            }
            
            // Temporarily set credentials for testing
            $originalUsername = Mage::getStoreConfig('pfg/core/bitbucket_username');
            $originalPassword = Mage::getStoreConfig('pfg/core/bitbucket_app_password');
            $originalWorkspace = Mage::getStoreConfig('pfg/core/bitbucket_workspace');
            $originalProject = Mage::getStoreConfig('pfg/core/bitbucket_project');
            
            // Set test values
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_username', $username);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_app_password', Mage::helper('core')->encrypt($appPassword));
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_workspace', $workspace);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_project', $project);
            
            // Test connection
            $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
            $result = $bitbucketHelper->testConnection();
            
            // Restore original values
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_username', $originalUsername);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_app_password', $originalPassword);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_workspace', $originalWorkspace);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_project', $originalProject);
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Connection test error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'message' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Main module manager page
     */
    public function indexAction()
    {
        $this->_title($this->__('PFG'))->_title($this->__('Module Manager'));
        
        $this->loadLayout();
        $this->_setActiveMenu('pfg/core');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Module Manager'), $this->__('Module Manager'));
        
        $this->renderLayout();
    }
    
    /**
     * Repository browser page
     */
    public function repositoriesAction()
    {
        $this->_title($this->__('PFG'))->_title($this->__('Repository Browser'));
        
        $this->loadLayout();
        $this->_setActiveMenu('pfg/repositories');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Repository Browser'), $this->__('Repository Browser'));
        
        $this->renderLayout();
    }
    
    /**
     * Get repositories via AJAX
     */
    public function getRepositoriesAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $repositoryModel = Mage::getModel('pfg_core/repository');
            $result = $repositoryModel->getRepositoriesWithStatus();
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Get repositories error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Install module action
     */
    public function installModuleAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $request = $this->getRequest();
            $repositoryName = $request->getParam('repository');
            $version = $request->getParam('version', 'master');
            
            if (empty($repositoryName)) {
                throw new Exception($this->__('Repository name is required'));
            }
            
            $installationModel = Mage::getModel('pfg_core/installation');
            $result = $installationModel->installModule($repositoryName, $version);
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Install module error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Update module action
     */
    public function updateModuleAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $request = $this->getRequest();
            $repositoryName = $request->getParam('repository');
            $version = $request->getParam('version');
            
            if (empty($repositoryName) || empty($version)) {
                throw new Exception($this->__('Repository name and version are required'));
            }
            
            $installationModel = Mage::getModel('pfg_core/installation');
            $result = $installationModel->updateModule($repositoryName, $version);
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Update module error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Installation history page
     */
    public function installationsAction()
    {
        $this->_title($this->__('PFG'))->_title($this->__('Installation History'));
        
        $this->loadLayout();
        $this->_setActiveMenu('pfg/installations');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Installation History'), $this->__('Installation History'));
        
        $this->renderLayout();
    }
    
    /**
     * Backup management page
     */
    public function backupsAction()
    {
        $this->_title($this->__('PFG'))->_title($this->__('Backup Management'));
        
        $this->loadLayout();
        $this->_setActiveMenu('pfg/backups');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Backup Management'), $this->__('Backup Management'));
        
        $this->renderLayout();
    }
    
    /**
     * Rollback installation action
     */
    public function rollbackAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $installationId = $this->getRequest()->getParam('installation_id');
            
            if (empty($installationId)) {
                throw new Exception($this->__('Installation ID is required'));
            }
            
            $installationModel = Mage::getModel('pfg_core/installation')->load($installationId);
            
            if (!$installationModel->getId()) {
                throw new Exception($this->__('Installation not found'));
            }
            
            $result = $installationModel->rollback();
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Rollback error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Clear caches and logout admin users
     */
    public function postInstallActionsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            // Clear all caches
            Mage::app()->getCacheInstance()->flush();
            
            // Clear Redis cache if available
            try {
                if (class_exists('Cm_Cache_Backend_Redis')) {
                    $redis = new Redis();
                    $redis->connect('127.0.0.1', 6379);
                    $redis->flushAll();
                    $redis->close();
                }
            } catch (Exception $e) {
                // Redis not available or connection failed, continue
            }
            
            // Force logout of all admin users for security
            $adminSessions = Mage::getResourceModel('admin/session_collection');
            foreach ($adminSessions as $session) {
                $session->delete();
            }
            
            $result = array(
                'success' => true,
                'message' => $this->__('Caches cleared and admin users logged out successfully')
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Post-install actions error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * View installation details
     */
    public function viewInstallationAction()
    {
        $id = $this->getRequest()->getParam('id');
        $installation = Mage::getModel('pfg_core/installation')->load($id);

        if (!$installation->getId()) {
            $this->_getSession()->addError($this->__('Installation not found'));
            $this->_redirect('*/*/installations');
            return;
        }

        Mage::register('current_installation', $installation);

        $this->_title($this->__('PFG'))->_title($this->__('Installation Details'));

        $this->loadLayout();
        $this->_setActiveMenu('pfg/installations');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Installation History'), $this->__('Installation History'));
        $this->_addBreadcrumb($this->__('Installation Details'), $this->__('Installation Details'));

        $this->renderLayout();
    }

    /**
     * Cleanup old installation records
     */
    public function cleanupInstallationsAction()
    {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime('-30 days'));

            $collection = Mage::getModel('pfg_core/installation')->getCollection()
                ->addFieldToFilter('created_at', array('lt' => $cutoffDate))
                ->addFieldToFilter('installation_status', array('in' => array(
                    PFG_Core_Model_Installation::STATUS_COMPLETED,
                    PFG_Core_Model_Installation::STATUS_FAILED,
                    PFG_Core_Model_Installation::STATUS_ROLLED_BACK
                )));

            $deletedCount = 0;
            foreach ($collection as $installation) {
                $installation->delete();
                $deletedCount++;
            }

            $this->_getSession()->addSuccess($this->__('Cleaned up %d old installation records', $deletedCount));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to cleanup installation records: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/installations');
    }

    /**
     * Mass delete installation records
     */
    public function massDeleteInstallationsAction()
    {
        try {
            $installationIds = $this->getRequest()->getParam('installation_ids');

            if (!is_array($installationIds) || empty($installationIds)) {
                throw new Exception($this->__('Please select installation records to delete'));
            }

            $deletedCount = 0;
            foreach ($installationIds as $id) {
                $installation = Mage::getModel('pfg_core/installation')->load($id);
                if ($installation->getId()) {
                    $installation->delete();
                    $deletedCount++;
                }
            }

            $this->_getSession()->addSuccess($this->__('Deleted %d installation records', $deletedCount));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to delete installation records: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/installations');
    }

    /**
     * Download backup file
     */
    public function downloadBackupAction()
    {
        try {
            $id = $this->getRequest()->getParam('id');
            $backup = Mage::getModel('pfg_core/backup')->load($id);

            if (!$backup->getId()) {
                throw new Exception($this->__('Backup not found'));
            }

            $filePath = $backup->getBackupPath();

            if (!file_exists($filePath)) {
                throw new Exception($this->__('Backup file not found'));
            }

            $fileName = basename($filePath);

            $this->getResponse()
                ->setHttpResponseCode(200)
                ->setHeader('Pragma', 'public', true)
                ->setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0', true)
                ->setHeader('Content-type', 'application/octet-stream', true)
                ->setHeader('Content-Length', filesize($filePath), true)
                ->setHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"', true)
                ->setHeader('Last-Modified', date('r', filemtime($filePath)), true);

            $this->getResponse()->clearBody();
            $this->getResponse()->sendHeaders();

            readfile($filePath);
            exit;

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to download backup: %s', $e->getMessage()));
            $this->_redirect('*/*/backups');
        }
    }

    /**
     * Restore from backup
     */
    public function restoreBackupAction()
    {
        try {
            $id = $this->getRequest()->getParam('id');
            $backup = Mage::getModel('pfg_core/backup')->load($id);

            if (!$backup->getId()) {
                throw new Exception($this->__('Backup not found'));
            }

            $result = $backup->restoreBackup($id);

            if ($result['success']) {
                $this->_getSession()->addSuccess($result['message']);
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to restore backup: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/backups');
    }

    /**
     * Cleanup old backups
     */
    public function cleanupBackupsAction()
    {
        try {
            $backupModel = Mage::getModel('pfg_core/backup');
            $result = $backupModel->cleanupOldBackups();

            if ($result['success']) {
                $this->_getSession()->addSuccess($this->__('Cleaned up %d old backups, freed %s',
                    $result['deleted_count'],
                    Mage::helper('pfg_core')->formatFileSize($result['freed_space'])
                ));
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to cleanup backups: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/backups');
    }

    /**
     * Mass delete backups
     */
    public function massDeleteBackupsAction()
    {
        try {
            $backupIds = $this->getRequest()->getParam('backup_ids');

            if (!is_array($backupIds) || empty($backupIds)) {
                throw new Exception($this->__('Please select backups to delete'));
            }

            $deletedCount = 0;
            $freedSpace = 0;

            foreach ($backupIds as $id) {
                $backup = Mage::getModel('pfg_core/backup')->load($id);
                if ($backup->getId()) {
                    if (file_exists($backup->getBackupPath())) {
                        $freedSpace += filesize($backup->getBackupPath());
                        unlink($backup->getBackupPath());
                    }
                    $backup->delete();
                    $deletedCount++;
                }
            }

            $this->_getSession()->addSuccess($this->__('Deleted %d backups, freed %s',
                $deletedCount,
                Mage::helper('pfg_core')->formatFileSize($freedSpace)
            ));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to delete backups: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/backups');
    }

    /**
     * Check admin permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('pfg/core');
    }
}
