<?php
/**
 * Test Bitbucket Download Functionality
 * Run this script to test the repository download process
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

echo "=== Bitbucket Download Test ===\n\n";

try {
    $helper = Mage::helper('pfg_core');
    $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
    
    // Check if credentials are configured
    if (!$helper->hasCredentials()) {
        echo "❌ Error: Bitbucket credentials not configured\n";
        echo "Please configure credentials in System > Configuration > PFG > Core Module Management\n";
        exit(1);
    }
    
    echo "✅ Credentials configured\n";
    echo "   Username: " . $helper->getBitbucketUsername() . "\n";
    echo "   Workspace: " . $helper->getBitbucketWorkspace() . "\n";
    echo "   Project: " . $helper->getBitbucketProject() . "\n\n";
    
    // Test connection
    echo "🔗 Testing connection...\n";
    $connectionTest = $bitbucketHelper->testConnection();
    if ($connectionTest['success']) {
        echo "✅ Connection successful: " . $connectionTest['message'] . "\n\n";
    } else {
        echo "❌ Connection failed: " . $connectionTest['message'] . "\n";
        exit(1);
    }
    
    // Get repositories
    echo "📂 Getting repositories...\n";
    $repositories = $bitbucketHelper->getRepositories();
    if ($repositories['success']) {
        echo "✅ Found " . count($repositories['data']) . " repositories\n";
        foreach ($repositories['data'] as $repo) {
            echo "   - " . $repo['name'] . "\n";
        }
        echo "\n";
    } else {
        echo "❌ Failed to get repositories: " . $repositories['error'] . "\n";
        exit(1);
    }
    
    // Test downloading the first repository
    if (!empty($repositories['data'])) {
        $testRepo = $repositories['data'][0];
        echo "📥 Testing download of repository: " . $testRepo['name'] . "\n";
        
        $downloadResult = $bitbucketHelper->downloadRepository($testRepo['name'], 'master');
        if ($downloadResult['success']) {
            echo "✅ Download successful!\n";
            echo "   File: " . $downloadResult['filepath'] . "\n";
            echo "   Size: " . number_format($downloadResult['size']) . " bytes\n";
            
            // Test ZIP file
            echo "🗜️  Testing ZIP file...\n";
            $zip = new ZipArchive();
            $result = $zip->open($downloadResult['filepath']);
            if ($result === TRUE) {
                echo "✅ ZIP file is valid\n";
                echo "   Files in archive: " . $zip->numFiles . "\n";
                
                // List first few files
                echo "   Sample files:\n";
                for ($i = 0; $i < min(5, $zip->numFiles); $i++) {
                    $stat = $zip->statIndex($i);
                    echo "     - " . $stat['name'] . "\n";
                }
                $zip->close();
                
                // Clean up test file
                unlink($downloadResult['filepath']);
                echo "   Test file cleaned up\n";
            } else {
                echo "❌ ZIP file is invalid (error code: " . $result . ")\n";
            }
        } else {
            echo "❌ Download failed: " . $downloadResult['error'] . "\n";
        }
    }
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
